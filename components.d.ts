/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ChatBox: typeof import('./src/components/ChatBox.vue')['default']
    CommandPalette: typeof import('./src/components/CommandPalette.vue')['default']
    FileImage: typeof import('./src/components/FileImage.vue')['default']
    Hello: typeof import('./src/components/Hello.vue')['default']
    MarkdownText: typeof import('./src/components/MarkdownText.vue')['default']
    MessageBubble: typeof import('./src/components/MessageBubble.vue')['default']
    MessageEdit: typeof import('./src/components/MessageEdit.vue')['default']
    MessageList: typeof import('./src/components/MessageList.vue')['default']
    MessageSwitcher: typeof import('./src/components/MessageSwitcher.vue')['default']
    MilkdownEditor: typeof import('./src/components/MilkdownEditor.vue')['default']
    ModelSelector: typeof import('./src/components/ModelSelector.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Scrollbar: typeof import('./src/components/Scrollbar.vue')['default']
    Settings: typeof import('./src/components/Settings.vue')['default']
    Sidebar: typeof import('./src/components/Sidebar.vue')['default']
    Spinner: typeof import('./src/components/Spinner.vue')['default']
    Tabs: typeof import('./src/components/Tabs.vue')['default']
    ThemeSwitcher: typeof import('./src/components/ThemeSwitcher.vue')['default']
    UApp: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/App.vue')['default']
    UAvatar: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Avatar.vue')['default']
    UBadge: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Badge.vue')['default']
    UButton: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Button.vue')['default']
    UCollapsible: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Collapsible.vue')['default']
    UCommandPalette: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/CommandPalette.vue')['default']
    UForm: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Form.vue')['default']
    UFormField: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/FormField.vue')['default']
    UInput: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Input.vue')['default']
    UModal: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Modal.vue')['default']
    UploadButton: typeof import('./src/components/UploadButton.vue')['default']
    UPopover: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Popover.vue')['default']
    USelectMenu: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/SelectMenu.vue')['default']
    USlider: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Slider.vue')['default']
    USwitch: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Switch.vue')['default']
    UTabs: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Tabs.vue')['default']
    UTextarea: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Textarea.vue')['default']
    UTooltip: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_embla-carousel@8.6.0_typescript@5.6.3_vite@6.3.5_jiti@2.5_e2bxm4yffy3shlfcs3kb7gyroe/node_modules/@nuxt/ui/dist/runtime/components/Tooltip.vue')['default']
    WindowControls: typeof import('./src/components/WindowControls.vue')['default']
    WindowHeader: typeof import('./src/components/WindowHeader.vue')['default']
  }
}
