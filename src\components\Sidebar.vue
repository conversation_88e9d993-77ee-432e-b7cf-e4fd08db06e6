<script setup lang="ts">
import { useRouter } from "vue-router";
import { nanoid } from "nanoid";
import { useTabsStore } from "@/stores/tabs";
import { useSidebarStore } from "@/stores/sidebar";
import { Agent, Note } from "@/db";
import { tv } from "tailwind-variants";

const buttonStyle = tv({
  base: "items-center w-full",
});

const router = useRouter();
const tabsStore = useTabsStore();
const sidebarStore = useSidebarStore();

sidebarStore.load();

function addChat() {
  const id = nanoid();
  tabsStore.openTab(`/chat/${id}`, "New chat");
  router.push({
    name: "chat",
    params: { id },
  });
}

function addAgent() {
  const id = nanoid();
  tabsStore.openTab(`/agent/${id}`, "New agent");
  router.push({
    name: "agent",
    params: {
      id,
    },
  });
}

function agentUrl(agent: Agent) {
  return `/agent/${agent.id}`;
}

function openAgent(agent: Agent) {
  tabsStore.openTab(agentUrl(agent), agent.name);
  router.push({
    name: "agent",
    params: {
      id: agent.id,
    },
  });
}

function addNote() {
  const id = nanoid();
  tabsStore.openTab(`/note/${id}`, "New note");
  router.push({
    name: "note",
    params: {
      id,
    },
  });
}

function openNote(note: Note) {
  tabsStore.openTab(`/note/${note.id}`, note.name);
  router.push({
    name: "note",
    params: {
      id: note.id,
    },
  });
}
</script>

<template>
  <aside
    class="flex-1 size-full bg-elevated flex flex-col p-2 gap-2 items-stretch"
  >
    <h1 class="text-xl font-bold mb-2 flex flex-row gap-2 items-center">
      <img class="w-16 h-16" src="/raven.png" alt="Logo" />
      Raven
    </h1>
    <UButton
      icon="i-lucide-message-circle"
      variant="subtle"
      color="neutral"
      @click="addChat"
      >New Chat</UButton
    >
    <UCollapsible class="flex flex-col gap-2" default-open>
      <UButton
        class="group"
        icon="i-lucide-brain"
        variant="subtle"
        color="neutral"
        trailing-icon="i-lucide-chevron-down"
        :ui="{
          trailingIcon:
            'group-data-[state=open]:rotate-180 transition-transform duration-200',
        }"
        block
        >Agent</UButton
      >
      <template #content>
        <section class="space-y-1">
          <UButton
            v-for="agent in sidebarStore.agents"
            :key="agent.id"
            :class="buttonStyle({})"
            :icon="agent.icon"
            color="neutral"
            variant="soft"
            active-color="primary"
            active-variant="solid"
            :active="$route.path === agentUrl(agent)"
            @click="openAgent(agent)"
            >{{ agent.name }}</UButton
          >
          <UButton
            :class="buttonStyle({})"
            icon="i-lucide-plus"
            color="neutral"
            variant="soft"
            @click="addAgent"
            >Add</UButton
          >
        </section>
      </template>
    </UCollapsible>
    <UCollapsible class="flex flex-col gap-2" default-open>
      <UButton
        class="group"
        icon="i-lucide-notebook-text"
        variant="subtle"
        color="neutral"
        :ui="{
          trailingIcon:
            'group-data-[state=open]:rotate-180 transition-transform duration-200',
        }"
        trailing-icon="i-lucide-chevron-down"
        default-open
        block
      >
        Note
      </UButton>
      <template #content>
        <section class="space-y-1">
          <UButton
            :class="buttonStyle({})"
            icon="i-lucide-plus"
            color="neutral"
            variant="soft"
            @click="addNote"
            >Add</UButton
          >
        </section>
      </template>
    </UCollapsible>
  </aside>
</template>
