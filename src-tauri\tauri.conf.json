{"$schema": "https://schema.tauri.app/config/2", "productName": "tauri-app", "version": "0.1.0", "identifier": "com.tauri-app.app", "build": {"beforeDevCommand": "yarn dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "yarn build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "tauri-app", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "decorations": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}