{"name": "tauri-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/vue": "^1.2.12", "@milkdown/crepe": "^7.15.2", "@milkdown/kit": "^7.15.2", "@milkdown/vue": "^7.15.2", "@nuxt/ui": "^3.1.3", "@openrouter/ai-sdk-provider": "^0.7.0", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "~2", "@tauri-apps/plugin-opener": "^2", "@vueuse/core": "^13.3.0", "ai": "^4.3.16", "dedent": "^1.6.0", "lodash-es": "^4.17.21", "marked": "^15.0.12", "marked-highlight": "^2.2.1", "mitt": "^3.0.1", "moment": "^2.30.1", "motion-v": "^1.1.1", "nanoid": "^5.1.5", "pinia": "^3.0.3", "reka-ui": "^2.3.1", "shiki": "^3.6.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.8", "vue": "^3.5.13", "vue-easy-lightbox": "^1.19.0", "vue-router": "^4.5.1", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/vite": "^4.1.8", "@tauri-apps/cli": "^2", "@types/lodash-es": "^4", "@vitejs/plugin-vue": "^5.2.1", "clsx": "^2.1.1", "sass-embedded": "^1.89.2", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "~5.6.2", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}